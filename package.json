{"name": "nextjs-app-type", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --pretty --noEmit", "check-format": "prettier --check .", "check-lint": "eslint . --ext ts --ext tsx --ext js", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test-all": "npm run check-format && npm run check-lint && npm run check-types && npm run test:ci && npm run build", "prepare": "husky install"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.28.14", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.0.4", "framer-motion": "^12.0.6", "input-otp": "^1.4.2", "lucide-react": "^0.363.0", "next": "^14.2.18", "next-auth": "^4.24.7", "next-themes": "^0.2.1", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.51.2", "sharp": "^0.33.3", "sonner": "^1.4.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-google": "^0.14.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "husky": "^9.0.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8", "prettier": "^3.2.4", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}